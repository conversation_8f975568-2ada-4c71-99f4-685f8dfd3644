import WebSocket from 'ws';

const WS_URL = 'ws://localhost:3000';

// Test users
const testUser1 = { id: 1, username: 'testuser1' };
const testUser2 = { id: 2, username: 'testuser2' };

let user1Socket = null;
let user2Socket = null;

function connectUser(userId, username) {
    return new Promise((resolve, reject) => {
        console.log(`🔌 Connecting user ${username} (ID: ${userId})...`);
        const socket = new WebSocket(WS_URL);

        socket.onopen = function() {
            console.log(`✅ User ${username} connected`);
            
            // Send status message to register user
            socket.send(JSON.stringify({
                type: 'status',
                userId: userId
            }));
            
            resolve(socket);
        };

        socket.onmessage = function(event) {
            const message = JSON.parse(event.data);
            console.log(`📨 User ${username} received:`, message);
        };

        socket.onclose = function(event) {
            console.log(`🔌 User ${username} disconnected:`, event.code, event.reason);
        };

        socket.onerror = function(error) {
            console.error(`❌ User ${username} WebSocket error:`, error);
            reject(error);
        };

        // Timeout after 5 seconds
        setTimeout(() => {
            if (socket.readyState !== WebSocket.OPEN) {
                reject(new Error(`Connection timeout for user ${username}`));
            }
        }, 5000);
    });
}

function sendMessage(socket, senderId, receiverId, content, senderName) {
    return new Promise((resolve) => {
        console.log(`📤 Sending message from ${senderName}: "${content}"`);
        
        socket.send(JSON.stringify({
            type: 'chat',
            receiverId: receiverId,
            content: content,
            messageType: 'text',
            userId: senderId,
            senderName: senderName
        }));
        
        // Wait a bit for the message to be processed
        setTimeout(resolve, 1000);
    });
}

function sendTypingIndicator(socket, senderId, receiverId, isTyping, senderName) {
    return new Promise((resolve) => {
        const action = isTyping ? 'started' : 'stopped';
        console.log(`⌨️  User ${senderName} ${action} typing`);
        
        socket.send(JSON.stringify({
            type: isTyping ? 'typing' : 'typing_stopped',
            receiverId: receiverId,
            userId: senderId,
            senderName: senderName
        }));
        
        // Wait a bit for the indicator to be processed
        setTimeout(resolve, 500);
    });
}

async function testMessaging() {
    console.log('\n🧪 Testing WebSocket Messaging...');
    
    try {
        // Connect both users
        user1Socket = await connectUser(testUser1.id, testUser1.username);
        user2Socket = await connectUser(testUser2.id, testUser2.username);
        
        // Wait for connections to stabilize
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test typing indicators
        console.log('\n📝 Testing typing indicators...');
        await sendTypingIndicator(user1Socket, testUser1.id, testUser2.id, true, testUser1.username);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate typing for 2 seconds
        await sendTypingIndicator(user1Socket, testUser1.id, testUser2.id, false, testUser1.username);
        
        // Test message sending
        console.log('\n💬 Testing message sending...');
        await sendMessage(user1Socket, testUser1.id, testUser2.id, 'Hello from User 1!', testUser1.username);
        await sendMessage(user2Socket, testUser2.id, testUser1.id, 'Hi there, User 1!', testUser2.username);
        await sendMessage(user1Socket, testUser1.id, testUser2.id, 'How are you doing?', testUser1.username);
        
        // Test typing and then sending message
        console.log('\n⌨️💬 Testing typing then message...');
        await sendTypingIndicator(user2Socket, testUser2.id, testUser1.id, true, testUser2.username);
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate typing
        await sendMessage(user2Socket, testUser2.id, testUser1.id, 'I am doing great, thanks!', testUser2.username);
        
        console.log('\n✅ Messaging test completed successfully');
        
    } catch (error) {
        console.error('❌ Messaging test failed:', error.message);
    }
}

async function cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (user1Socket) {
        user1Socket.close();
    }
    if (user2Socket) {
        user2Socket.close();
    }
    
    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));
}

async function runTests() {
    console.log('🚀 Starting WebSocket Messaging Tests...\n');

    await testMessaging();
    await cleanup();

    console.log('\n🏁 All tests completed!');
    process.exit(0);
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n🛑 Test interrupted');
    await cleanup();
    process.exit(0);
});

// Run tests
runTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
