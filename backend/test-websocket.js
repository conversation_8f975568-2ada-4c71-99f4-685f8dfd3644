// Simple WebSocket test script to verify connection consistency
import WebSocket from 'ws';

const WS_URL = 'ws://localhost:3000';
let testSocket = null;

// Test user data
const testUser = {
    id: 1,
    name: 'Test User'
};

function connectTestSocket() {
    return new Promise((resolve, reject) => {
        console.log('🔌 Connecting to WebSocket server...');
        testSocket = new WebSocket(WS_URL);

        testSocket.onopen = function() {
            console.log('✅ WebSocket connection established');
            resolve();
        };

        testSocket.onmessage = function(event) {
            const message = JSON.parse(event.data);
            console.log('📨 Received message:', message);
        };

        testSocket.onclose = function(event) {
            console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        };

        testSocket.onerror = function(error) {
            console.error('❌ WebSocket error:', error);
            reject(error);
        };

        // Timeout after 5 seconds
        setTimeout(() => {
            if (testSocket.readyState !== WebSocket.OPEN) {
                reject(new Error('Connection timeout'));
            }
        }, 5000);
    });
}

function sendTestMessage(type, data = {}) {
    return new Promise((resolve, reject) => {
        if (!testSocket || testSocket.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket not connected'));
            return;
        }

        const message = {
            type: type,
            userId: testUser.id,
            ...data
        };

        console.log(`📤 Sending ${type} message:`, message);
        testSocket.send(JSON.stringify(message));
        
        // Wait for response
        setTimeout(resolve, 1000);
    });
}

async function testSignInFlow() {
    console.log('\n🧪 Testing Sign-In Flow...');

    try {
        // Connect to WebSocket
        await connectTestSocket();

        // Test connection
        await sendTestMessage('testConnection');

        // Send status message (simulating sign-in)
        await sendTestMessage('status');

        // Wait a bit longer to see if we get the status response
        await new Promise(resolve => setTimeout(resolve, 2000));

        console.log('✅ Sign-in flow test completed successfully');

    } catch (error) {
        console.error('❌ Sign-in flow test failed:', error.message);
    }
}

async function testLogoutFlow() {
    console.log('\n🧪 Testing Logout Flow...');
    
    try {
        // Send logout message
        await sendTestMessage('logout');
        
        // Wait for connection to close
        await new Promise(resolve => {
            if (testSocket.readyState === WebSocket.CLOSED) {
                resolve();
            } else {
                testSocket.onclose = resolve;
            }
            setTimeout(resolve, 2000); // Timeout after 2 seconds
        });
        
        console.log('✅ Logout flow test completed successfully');
        
    } catch (error) {
        console.error('❌ Logout flow test failed:', error.message);
    }
}

async function testReconnection() {
    console.log('\n🧪 Testing Reconnection...');
    
    try {
        // Connect again after logout
        await connectTestSocket();
        
        // Test connection
        await sendTestMessage('testConnection');
        
        // Send status message again
        await sendTestMessage('status');
        
        console.log('✅ Reconnection test completed successfully');
        
        // Clean up
        if (testSocket) {
            testSocket.close();
        }
        
    } catch (error) {
        console.error('❌ Reconnection test failed:', error.message);
    }
}

async function testMultiUserStatusBroadcast() {
    console.log('\n🧪 Testing Multi-User Status Broadcasting...');

    let user1Socket = null;
    let user2Socket = null;

    try {
        // Create two WebSocket connections to simulate two users
        console.log('Creating User 1 connection...');
        user1Socket = new WebSocket(WS_URL);
        await new Promise((resolve, reject) => {
            user1Socket.onopen = resolve;
            user1Socket.onerror = reject;
            setTimeout(() => reject(new Error('User 1 connection timeout')), 5000);
        });

        console.log('Creating User 2 connection...');
        user2Socket = new WebSocket(WS_URL);
        await new Promise((resolve, reject) => {
            user2Socket.onopen = resolve;
            user2Socket.onerror = reject;
            setTimeout(() => reject(new Error('User 2 connection timeout')), 5000);
        });

        // Set up message listeners
        const user2Messages = [];
        user2Socket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            user2Messages.push(message);
            console.log('User 2 received:', message);
        };

        // User 1 sends status message
        console.log('User 1 sending status message...');
        user1Socket.send(JSON.stringify({
            type: 'status',
            userId: 1
        }));

        // User 2 also sends status message (different user ID)
        console.log('User 2 sending status message...');
        user2Socket.send(JSON.stringify({
            type: 'status',
            userId: 2
        }));

        // Wait for broadcast
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if User 2 received the status update from User 1
        const statusUpdate = user2Messages.find(msg => msg.type === 'user_status_update' && msg.userId === 1);
        if (statusUpdate) {
            console.log('✅ User 2 received status update from User 1:', statusUpdate);
        } else {
            console.log('❌ User 2 did not receive status update from User 1');
            console.log('All messages received by User 2:', user2Messages);
        }

        console.log('✅ Multi-user status broadcast test completed');

    } catch (error) {
        console.error('❌ Multi-user status broadcast test failed:', error.message);
    } finally {
        if (user1Socket) user1Socket.close();
        if (user2Socket) user2Socket.close();
    }
}

async function runTests() {
    console.log('🚀 Starting WebSocket Consistency Tests...\n');

    await testSignInFlow();
    await testLogoutFlow();
    await testReconnection();
    await testMultiUserStatusBroadcast();

    console.log('\n🏁 All tests completed!');
    process.exit(0);
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted');
    if (testSocket) {
        testSocket.close();
    }
    process.exit(0);
});

// Run tests
runTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
