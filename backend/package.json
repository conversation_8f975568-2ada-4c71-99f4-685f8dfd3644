{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcrypt": "^6.0.0", "better-sqlite3": "^12.2.0", "console": "^0.7.2", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "ws": "^8.18.3"}}